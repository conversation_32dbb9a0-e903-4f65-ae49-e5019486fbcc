{"version": 3, "file": "index.esm5.js", "sources": ["../../src/component.ts", "../../src/constants.ts", "../../src/provider.ts", "../../src/component_container.ts"], "sourcesContent": ["/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport {\n  InstantiationMode,\n  InstanceFactory,\n  ComponentType,\n  Dictionary,\n  Name,\n  onInstanceCreatedCallback\n} from './types';\n\n/**\n * Component for service name T, e.g. `auth`, `auth-internal`\n */\nexport class Component<T extends Name = Name> {\n  multipleInstances = false;\n  /**\n   * Properties to be added to the service namespace\n   */\n  serviceProps: Dictionary = {};\n\n  instantiationMode = InstantiationMode.LAZY;\n\n  onInstanceCreated: onInstanceCreatedCallback<T> | null = null;\n\n  /**\n   *\n   * @param name The public service name, e.g. app, auth, firestore, database\n   * @param instanceFactory Service factory responsible for creating the public interface\n   * @param type whether the service provided by the component is public or private\n   */\n  constructor(\n    readonly name: T,\n    readonly instanceFactory: InstanceFactory<T>,\n    readonly type: ComponentType\n  ) {}\n\n  setInstantiationMode(mode: InstantiationMode): this {\n    this.instantiationMode = mode;\n    return this;\n  }\n\n  setMultipleInstances(multipleInstances: boolean): this {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n\n  setServiceProps(props: Dictionary): this {\n    this.serviceProps = props;\n    return this;\n  }\n\n  setInstanceCreatedCallback(callback: onInstanceCreatedCallback<T>): this {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport const DEFAULT_ENTRY_NAME = '[DEFAULT]';\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Deferred } from '@firebase/util';\nimport { ComponentContainer } from './component_container';\nimport { DEFAULT_ENTRY_NAME } from './constants';\nimport {\n  InitializeOptions,\n  InstantiationMode,\n  Name,\n  NameServiceMapping,\n  OnInitCallBack\n} from './types';\nimport { Component } from './component';\n\n/**\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\n * NameServiceMapping[T] is an alias for the type of the instance\n */\nexport class Provider<T extends Name> {\n  private component: Component<T> | null = null;\n  private readonly instances: Map<string, NameServiceMapping[T]> = new Map();\n  private readonly instancesDeferred: Map<\n    string,\n    Deferred<NameServiceMapping[T]>\n  > = new Map();\n  private readonly instancesOptions: Map<string, Record<string, unknown>> =\n    new Map();\n  private onInitCallbacks: Map<string, Set<OnInitCallBack<T>>> = new Map();\n\n  constructor(\n    private readonly name: T,\n    private readonly container: ComponentContainer\n  ) {}\n\n  /**\n   * @param identifier A provider can provide mulitple instances of a service\n   * if this.component.multipleInstances is true.\n   */\n  get(identifier?: string): Promise<NameServiceMapping[T]> {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred<NameServiceMapping[T]>();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n\n      if (\n        this.isInitialized(normalizedIdentifier) ||\n        this.shouldAutoInitialize()\n      ) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n\n    return this.instancesDeferred.get(normalizedIdentifier)!.promise;\n  }\n\n  /**\n   *\n   * @param options.identifier A provider can provide mulitple instances of a service\n   * if this.component.multipleInstances is true.\n   * @param options.optional If optional is false or not provided, the method throws an error when\n   * the service is not immediately available.\n   * If optional is true, the method returns null if the service is not immediately available.\n   */\n  getImmediate(options: {\n    identifier?: string;\n    optional: true;\n  }): NameServiceMapping[T] | null;\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: false;\n  }): NameServiceMapping[T];\n  getImmediate(options?: {\n    identifier?: string;\n    optional?: boolean;\n  }): NameServiceMapping[T] | null {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      options?.identifier\n    );\n    const optional = options?.optional ?? false;\n\n    if (\n      this.isInitialized(normalizedIdentifier) ||\n      this.shouldAutoInitialize()\n    ) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/can not be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n\n  getComponent(): Component<T> | null {\n    return this.component;\n  }\n\n  setComponent(component: Component<T>): void {\n    if (component.name !== this.name) {\n      throw Error(\n        `Mismatching Component ${component.name} for Provider ${this.name}.`\n      );\n    }\n\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n\n    this.component = component;\n\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        })!;\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n\n  clearInstance(identifier: string = DEFAULT_ENTRY_NAME): void {\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  }\n\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  async delete(): Promise<void> {\n    const services = Array.from(this.instances.values());\n\n    await Promise.all([\n      ...services\n        .filter(service => 'INTERNAL' in service) // legacy services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any).INTERNAL!.delete()),\n      ...services\n        .filter(service => '_delete' in service) // modularized services\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        .map(service => (service as any)._delete())\n    ]);\n  }\n\n  isComponentSet(): boolean {\n    return this.component != null;\n  }\n\n  isInitialized(identifier: string = DEFAULT_ENTRY_NAME): boolean {\n    return this.instances.has(identifier);\n  }\n\n  getOptions(identifier: string = DEFAULT_ENTRY_NAME): Record<string, unknown> {\n    return this.instancesOptions.get(identifier) || {};\n  }\n\n  initialize(opts: InitializeOptions = {}): NameServiceMapping[T] {\n    const { options = {} } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(\n      opts.instanceIdentifier\n    );\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(\n        `${this.name}(${normalizedIdentifier}) has already been initialized`\n      );\n    }\n\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    })!;\n\n    // resolve any pending promise waiting for the service instance\n    for (const [\n      instanceIdentifier,\n      instanceDeferred\n    ] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier =\n        this.normalizeInstanceIdentifier(instanceIdentifier);\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n\n    return instance;\n  }\n\n  /**\n   *\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\n   *\n   * @param identifier An optional instance identifier\n   * @returns a function to unregister the callback\n   */\n  onInit(callback: OnInitCallBack<T>, identifier?: string): () => void {\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks =\n      this.onInitCallbacks.get(normalizedIdentifier) ??\n      new Set<OnInitCallBack<T>>();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n\n  /**\n   * Invoke onInit callbacks synchronously\n   * @param instance the service instance`\n   */\n  private invokeOnInitCallbacks(\n    instance: NameServiceMapping[T],\n    identifier: string\n  ): void {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n\n  private getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }: {\n    instanceIdentifier: string;\n    options?: Record<string, unknown>;\n  }): NameServiceMapping[T] | null {\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance);\n      this.instancesOptions.set(instanceIdentifier, options);\n\n      /**\n       * Invoke onInit listeners.\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\n       * while onInit listeners are registered by consumers of the provider.\n       */\n      this.invokeOnInitCallbacks(instance, instanceIdentifier);\n\n      /**\n       * Order is important\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\n       * makes `isInitialized()` return true.\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(\n            this.container,\n            instanceIdentifier,\n            instance\n          );\n        } catch {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n\n    return instance || null;\n  }\n\n  private normalizeInstanceIdentifier(\n    identifier: string = DEFAULT_ENTRY_NAME\n  ): string {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n\n  private shouldAutoInitialize(): boolean {\n    return (\n      !!this.component &&\n      this.component.instantiationMode !== InstantiationMode.EXPLICIT\n    );\n  }\n}\n\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier: string): string | undefined {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\n\nfunction isComponentEager<T extends Name>(component: Component<T>): boolean {\n  return component.instantiationMode === InstantiationMode.EAGER;\n}\n", "/**\n * @license\n * Copyright 2019 Google LLC\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Provider } from './provider';\nimport { Component } from './component';\nimport { Name } from './types';\n\n/**\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\n */\nexport class ComponentContainer {\n  private readonly providers = new Map<string, Provider<Name>>();\n\n  constructor(private readonly name: string) {}\n\n  /**\n   *\n   * @param component Component being added\n   * @param overwrite When a component with the same name has already been registered,\n   * if overwrite is true: overwrite the existing component with the new component and create a new\n   * provider with the new component. It can be useful in tests where you want to use different mocks\n   * for different tests.\n   * if overwrite is false: throw an exception\n   */\n  addComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(\n        `Component ${component.name} has already been registered with ${this.name}`\n      );\n    }\n\n    provider.setComponent(component);\n  }\n\n  addOrOverwriteComponent<T extends Name>(component: Component<T>): void {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n\n    this.addComponent(component);\n  }\n\n  /**\n   * getProvider provides a type safe interface where it can only be called with a field name\n   * present in NameServiceMapping interface.\n   *\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\n   * themselves.\n   */\n  getProvider<T extends Name>(name: T): Provider<T> {\n    if (this.providers.has(name)) {\n      return this.providers.get(name) as unknown as Provider<T>;\n    }\n\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider<T>(name, this);\n    this.providers.set(name, provider as unknown as Provider<Name>);\n\n    return provider as Provider<T>;\n  }\n\n  getProviders(): Array<Provider<Name>> {\n    return Array.from(this.providers.values());\n  }\n}\n"], "names": [], "mappings": ";;;AAyBA;;AAEG;AACH,IAAA,SAAA,kBAAA,YAAA;AAWE;;;;;AAKG;AACH,IAAA,SAAA,SAAA,CACW,IAAO,EACP,eAAmC,EACnC,IAAmB,EAAA;QAFnB,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAG;QACP,IAAe,CAAA,eAAA,GAAf,eAAe,CAAoB;QACnC,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAe;QAnB9B,IAAiB,CAAA,iBAAA,GAAG,KAAK,CAAC;AAC1B;;AAEG;QACH,IAAY,CAAA,YAAA,GAAe,EAAE,CAAC;AAE9B,QAAA,IAAA,CAAA,iBAAiB,GAA0B,MAAA,8BAAA;QAE3C,IAAiB,CAAA,iBAAA,GAAwC,IAAI,CAAC;KAY1D;IAEJ,SAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,IAAuB,EAAA;AAC1C,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAC9B,QAAA,OAAO,IAAI,CAAC;KACb,CAAA;IAED,SAAoB,CAAA,SAAA,CAAA,oBAAA,GAApB,UAAqB,iBAA0B,EAAA;AAC7C,QAAA,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;AAC3C,QAAA,OAAO,IAAI,CAAC;KACb,CAAA;IAED,SAAe,CAAA,SAAA,CAAA,eAAA,GAAf,UAAgB,KAAiB,EAAA;AAC/B,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;AAC1B,QAAA,OAAO,IAAI,CAAC;KACb,CAAA;IAED,SAA0B,CAAA,SAAA,CAAA,0BAAA,GAA1B,UAA2B,QAAsC,EAAA;AAC/D,QAAA,IAAI,CAAC,iBAAiB,GAAG,QAAQ,CAAC;AAClC,QAAA,OAAO,IAAI,CAAC;KACb,CAAA;IACH,OAAC,SAAA,CAAA;AAAD,CAAC,EAAA;;ACtED;;;;;;;;;;;;;;;AAeG;AAEI,IAAM,kBAAkB,GAAG,WAAW;;ACjB7C;;;;;;;;;;;;;;;AAeG;AAcH;;;AAGG;AACH,IAAA,QAAA,kBAAA,YAAA;IAWE,SACmB,QAAA,CAAA,IAAO,EACP,SAA6B,EAAA;QAD7B,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAG;QACP,IAAS,CAAA,SAAA,GAAT,SAAS,CAAoB;QAZxC,IAAS,CAAA,SAAA,GAAwB,IAAI,CAAC;AAC7B,QAAA,IAAA,CAAA,SAAS,GAAuC,IAAI,GAAG,EAAE,CAAC;AAC1D,QAAA,IAAA,CAAA,iBAAiB,GAG9B,IAAI,GAAG,EAAE,CAAC;AACG,QAAA,IAAA,CAAA,gBAAgB,GAC/B,IAAI,GAAG,EAAE,CAAC;AACJ,QAAA,IAAA,CAAA,eAAe,GAAwC,IAAI,GAAG,EAAE,CAAC;KAKrE;AAEJ;;;AAGG;IACH,QAAG,CAAA,SAAA,CAAA,GAAA,GAAH,UAAI,UAAmB,EAAA;;QAErB,IAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;QAE1E,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,CAAC,EAAE;AACrD,YAAA,IAAM,QAAQ,GAAG,IAAI,QAAQ,EAAyB,CAAC;YACvD,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,EAAE,QAAQ,CAAC,CAAC;AAE3D,YAAA,IACE,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;gBACxC,IAAI,CAAC,oBAAoB,EAAE,EAC3B;;gBAEA,IAAI;AACF,oBAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC3C,wBAAA,kBAAkB,EAAE,oBAAoB;AACzC,qBAAA,CAAC,CAAC;AACH,oBAAA,IAAI,QAAQ,EAAE;AACZ,wBAAA,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AAC5B,qBAAA;AACF,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;;;AAGX,iBAAA;AACF,aAAA;AACF,SAAA;QAED,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,oBAAoB,CAAE,CAAC,OAAO,CAAC;KAClE,CAAA;IAkBD,QAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,OAGZ,EAAA;;;AAEC,QAAA,IAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAC3D,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,UAAU,CACpB,CAAC;AACF,QAAA,IAAM,QAAQ,GAAG,CAAA,EAAA,GAAA,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,OAAO,CAAE,QAAQ,MAAI,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAAA,KAAK,CAAC;AAE5C,QAAA,IACE,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACxC,IAAI,CAAC,oBAAoB,EAAE,EAC3B;YACA,IAAI;gBACF,OAAO,IAAI,CAAC,sBAAsB,CAAC;AACjC,oBAAA,kBAAkB,EAAE,oBAAoB;AACzC,iBAAA,CAAC,CAAC;AACJ,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;AACV,gBAAA,IAAI,QAAQ,EAAE;AACZ,oBAAA,OAAO,IAAI,CAAC;AACb,iBAAA;AAAM,qBAAA;AACL,oBAAA,MAAM,CAAC,CAAC;AACT,iBAAA;AACF,aAAA;AACF,SAAA;AAAM,aAAA;;AAEL,YAAA,IAAI,QAAQ,EAAE;AACZ,gBAAA,OAAO,IAAI,CAAC;AACb,aAAA;AAAM,iBAAA;gBACL,MAAM,KAAK,CAAC,UAAW,CAAA,MAAA,CAAA,IAAI,CAAC,IAAI,EAAA,mBAAA,CAAmB,CAAC,CAAC;AACtD,aAAA;AACF,SAAA;KACF,CAAA;AAED,IAAA,QAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;QACE,OAAO,IAAI,CAAC,SAAS,CAAC;KACvB,CAAA;IAED,QAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAAa,SAAuB,EAAA;;AAClC,QAAA,IAAI,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;AAChC,YAAA,MAAM,KAAK,CACT,wBAAyB,CAAA,MAAA,CAAA,SAAS,CAAC,IAAI,EAAiB,gBAAA,CAAA,CAAA,MAAA,CAAA,IAAI,CAAC,IAAI,EAAG,GAAA,CAAA,CACrE,CAAC;AACH,SAAA;QAED,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,KAAK,CAAC,gBAAiB,CAAA,MAAA,CAAA,IAAI,CAAC,IAAI,EAAA,4BAAA,CAA4B,CAAC,CAAC;AACrE,SAAA;AAED,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;;AAG3B,QAAA,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE;YAChC,OAAO;AACR,SAAA;;AAGD,QAAA,IAAI,gBAAgB,CAAC,SAAS,CAAC,EAAE;YAC/B,IAAI;gBACF,IAAI,CAAC,sBAAsB,CAAC,EAAE,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,CAAC;AACzE,aAAA;AAAC,YAAA,OAAO,CAAC,EAAE;;;;;AAKX,aAAA;AACF,SAAA;;;;;YAKD,KAGK,IAAA,EAAA,GAAA,QAAA,CAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAE,CAAA,EAAA,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAA;AAH5B,gBAAA,IAAA,KAAA,MAGV,CAAA,EAAA,CAAA,KAAA,EAAA,CAAA,CAAA,EAFC,kBAAkB,GAAA,EAAA,CAAA,CAAA,CAAA,EAClB,gBAAgB,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;gBAEhB,IAAM,oBAAoB,GACxB,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,CAAC;gBAEvD,IAAI;;AAEF,oBAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC3C,wBAAA,kBAAkB,EAAE,oBAAoB;AACzC,qBAAA,CAAE,CAAC;AACJ,oBAAA,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACpC,iBAAA;AAAC,gBAAA,OAAO,CAAC,EAAE;;;AAGX,iBAAA;AACF,aAAA;;;;;;;;;KACF,CAAA;IAED,QAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,UAAuC,EAAA;AAAvC,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAuC,GAAA,kBAAA,CAAA,EAAA;AACnD,QAAA,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AAC1C,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;AACzC,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;KACnC,CAAA;;;AAIK,IAAA,QAAA,CAAA,SAAA,CAAA,MAAM,GAAZ,YAAA;;;;;;AACQ,wBAAA,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;AAErD,wBAAA,OAAA,CAAA,CAAA,YAAM,OAAO,CAAC,GAAG,CAAA,aAAA,CAAA,aAAA,CAAA,EAAA,EAAA,MAAA,CACZ,QAAQ;AACR,iCAAA,MAAM,CAAC,UAAA,OAAO,EAAA,EAAI,OAAA,UAAU,IAAI,OAAO,CAArB,EAAqB,CAAC;;AAExC,iCAAA,GAAG,CAAC,UAAA,OAAO,EAAA,EAAI,OAAC,OAAe,CAAC,QAAS,CAAC,MAAM,EAAE,CAAA,EAAA,CAAC,kBACnD,QAAQ;AACR,iCAAA,MAAM,CAAC,UAAA,OAAO,EAAA,EAAI,OAAA,SAAS,IAAI,OAAO,CAApB,EAAoB,CAAC;;AAEvC,iCAAA,GAAG,CAAC,UAAA,OAAO,EAAA,EAAI,OAAC,OAAe,CAAC,OAAO,EAAE,CAAA,EAAA,CAAC,UAC7C,CAAA,CAAA;;AATF,wBAAA,EAAA,CAAA,IAAA,EASE,CAAC;;;;;AACJ,KAAA,CAAA;AAED,IAAA,QAAA,CAAA,SAAA,CAAA,cAAc,GAAd,YAAA;AACE,QAAA,OAAO,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC;KAC/B,CAAA;IAED,QAAa,CAAA,SAAA,CAAA,aAAA,GAAb,UAAc,UAAuC,EAAA;AAAvC,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAuC,GAAA,kBAAA,CAAA,EAAA;QACnD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;KACvC,CAAA;IAED,QAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,UAAuC,EAAA;AAAvC,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAuC,GAAA,kBAAA,CAAA,EAAA;QAChD,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;KACpD,CAAA;IAED,QAAU,CAAA,SAAA,CAAA,UAAA,GAAV,UAAW,IAA4B,EAAA;;AAA5B,QAAA,IAAA,IAAA,KAAA,KAAA,CAAA,EAAA,EAAA,IAA4B,GAAA,EAAA,CAAA,EAAA;QAC7B,IAAA,EAAA,GAAiB,IAAI,CAAT,OAAA,EAAZ,OAAO,GAAG,EAAA,KAAA,KAAA,CAAA,GAAA,EAAE,KAAA,CAAU;QAC9B,IAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAC3D,IAAI,CAAC,kBAAkB,CACxB,CAAC;AACF,QAAA,IAAI,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,EAAE;YAC5C,MAAM,KAAK,CACT,EAAA,CAAA,MAAA,CAAG,IAAI,CAAC,IAAI,EAAI,GAAA,CAAA,CAAA,MAAA,CAAA,oBAAoB,EAAgC,gCAAA,CAAA,CACrE,CAAC;AACH,SAAA;AAED,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,EAAE;YAC1B,MAAM,KAAK,CAAC,YAAa,CAAA,MAAA,CAAA,IAAI,CAAC,IAAI,EAAA,8BAAA,CAA8B,CAAC,CAAC;AACnE,SAAA;AAED,QAAA,IAAM,QAAQ,GAAG,IAAI,CAAC,sBAAsB,CAAC;AAC3C,YAAA,kBAAkB,EAAE,oBAAoB;AACxC,YAAA,OAAO,EAAA,OAAA;AACR,SAAA,CAAE,CAAC;;;YAGJ,KAGK,IAAA,EAAA,GAAA,QAAA,CAAA,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,CAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAE,CAAA,EAAA,CAAA,IAAA,EAAA,EAAA,GAAA,EAAA,CAAA,IAAA,EAAA,EAAA;AAH5B,gBAAA,IAAA,KAAA,MAGV,CAAA,EAAA,CAAA,KAAA,EAAA,CAAA,CAAA,EAFC,kBAAkB,GAAA,EAAA,CAAA,CAAA,CAAA,EAClB,gBAAgB,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;gBAEhB,IAAM,4BAA4B,GAChC,IAAI,CAAC,2BAA2B,CAAC,kBAAkB,CAAC,CAAC;gBACvD,IAAI,oBAAoB,KAAK,4BAA4B,EAAE;AACzD,oBAAA,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACpC,iBAAA;AACF,aAAA;;;;;;;;;AAED,QAAA,OAAO,QAAQ,CAAC;KACjB,CAAA;AAED;;;;;;;AAOG;AACH,IAAA,QAAA,CAAA,SAAA,CAAA,MAAM,GAAN,UAAO,QAA2B,EAAE,UAAmB,EAAA;;QACrD,IAAM,oBAAoB,GAAG,IAAI,CAAC,2BAA2B,CAAC,UAAU,CAAC,CAAC;AAC1E,QAAA,IAAM,iBAAiB,GACrB,CAAA,EAAA,GAAA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,oBAAoB,CAAC,MAAA,IAAA,IAAA,EAAA,KAAA,KAAA,CAAA,GAAA,EAAA,GAC9C,IAAI,GAAG,EAAqB,CAAC;AAC/B,QAAA,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAChC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,oBAAoB,EAAE,iBAAiB,CAAC,CAAC;QAElE,IAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;AAClE,QAAA,IAAI,gBAAgB,EAAE;AACpB,YAAA,QAAQ,CAAC,gBAAgB,EAAE,oBAAoB,CAAC,CAAC;AAClD,SAAA;QAED,OAAO,YAAA;AACL,YAAA,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrC,SAAC,CAAC;KACH,CAAA;AAED;;;AAGG;AACK,IAAA,QAAA,CAAA,SAAA,CAAA,qBAAqB,GAA7B,UACE,QAA+B,EAC/B,UAAkB,EAAA;;QAElB,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,SAAS,EAAE;YACd,OAAO;AACR,SAAA;;AACD,YAAA,KAAuB,IAAA,WAAA,GAAA,QAAA,CAAA,SAAS,CAAA,oCAAA,EAAE,CAAA,aAAA,CAAA,IAAA,EAAA,aAAA,GAAA,WAAA,CAAA,IAAA,EAAA,EAAA;AAA7B,gBAAA,IAAM,QAAQ,GAAA,aAAA,CAAA,KAAA,CAAA;gBACjB,IAAI;AACF,oBAAA,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;AAChC,iBAAA;gBAAC,OAAM,EAAA,EAAA;;AAEP,iBAAA;AACF,aAAA;;;;;;;;;KACF,CAAA;IAEO,QAAsB,CAAA,SAAA,CAAA,sBAAA,GAA9B,UAA+B,EAM9B,EAAA;AALC,QAAA,IAAA,kBAAkB,wBAAA,EAClB,EAAA,GAAA,EAAA,CAAA,OAAY,EAAZ,OAAO,GAAA,EAAA,KAAA,KAAA,CAAA,GAAG,EAAE,GAAA,EAAA,CAAA;QAKZ,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;AACtD,QAAA,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,EAAE;YAC/B,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,SAAS,EAAE;AACxD,gBAAA,kBAAkB,EAAE,6BAA6B,CAAC,kBAAkB,CAAC;AACrE,gBAAA,OAAO,EAAA,OAAA;AACR,aAAA,CAAC,CAAC;YACH,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YACjD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;AAEvD;;;;AAIG;AACH,YAAA,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,kBAAkB,CAAC,CAAC;AAEzD;;;;AAIG;AACH,YAAA,IAAI,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;gBACpC,IAAI;AACF,oBAAA,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAC9B,IAAI,CAAC,SAAS,EACd,kBAAkB,EAClB,QAAQ,CACT,CAAC;AACH,iBAAA;gBAAC,OAAM,EAAA,EAAA;;AAEP,iBAAA;AACF,aAAA;AACF,SAAA;QAED,OAAO,QAAQ,IAAI,IAAI,CAAC;KACzB,CAAA;IAEO,QAA2B,CAAA,SAAA,CAAA,2BAAA,GAAnC,UACE,UAAuC,EAAA;AAAvC,QAAA,IAAA,UAAA,KAAA,KAAA,CAAA,EAAA,EAAA,UAAuC,GAAA,kBAAA,CAAA,EAAA;QAEvC,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,OAAO,IAAI,CAAC,SAAS,CAAC,iBAAiB,GAAG,UAAU,GAAG,kBAAkB,CAAC;AAC3E,SAAA;AAAM,aAAA;YACL,OAAO,UAAU,CAAC;AACnB,SAAA;KACF,CAAA;AAEO,IAAA,QAAA,CAAA,SAAA,CAAA,oBAAoB,GAA5B,YAAA;AACE,QAAA,QACE,CAAC,CAAC,IAAI,CAAC,SAAS;AAChB,YAAA,IAAI,CAAC,SAAS,CAAC,iBAAiB,KAAA,UAAA,mCAChC;KACH,CAAA;IACH,OAAC,QAAA,CAAA;AAAD,CAAC,EAAA,EAAA;AAED;AACA,SAAS,6BAA6B,CAAC,UAAkB,EAAA;IACvD,OAAO,UAAU,KAAK,kBAAkB,GAAG,SAAS,GAAG,UAAU,CAAC;AACpE,CAAC;AAED,SAAS,gBAAgB,CAAiB,SAAuB,EAAA;AAC/D,IAAA,OAAO,SAAS,CAAC,iBAAiB,KAAA,OAAA,+BAA6B;AACjE;;ACzXA;;;;;;;;;;;;;;;AAeG;AAMH;;AAEG;AACH,IAAA,kBAAA,kBAAA,YAAA;AAGE,IAAA,SAAA,kBAAA,CAA6B,IAAY,EAAA;QAAZ,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAQ;AAFxB,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,GAAG,EAA0B,CAAC;KAElB;AAE7C;;;;;;;;AAQG;IACH,kBAAY,CAAA,SAAA,CAAA,YAAA,GAAZ,UAA6B,SAAuB,EAAA;QAClD,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAClD,QAAA,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE;AAC7B,YAAA,MAAM,IAAI,KAAK,CACb,YAAA,CAAA,MAAA,CAAa,SAAS,CAAC,IAAI,EAAA,oCAAA,CAAA,CAAA,MAAA,CAAqC,IAAI,CAAC,IAAI,CAAE,CAC5E,CAAC;AACH,SAAA;AAED,QAAA,QAAQ,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KAClC,CAAA;IAED,kBAAuB,CAAA,SAAA,CAAA,uBAAA,GAAvB,UAAwC,SAAuB,EAAA;QAC7D,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AAClD,QAAA,IAAI,QAAQ,CAAC,cAAc,EAAE,EAAE;;YAE7B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;AACvC,SAAA;AAED,QAAA,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC;KAC9B,CAAA;AAED;;;;;;AAMG;IACH,kBAAW,CAAA,SAAA,CAAA,WAAA,GAAX,UAA4B,IAAO,EAAA;QACjC,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAA2B,CAAC;AAC3D,SAAA;;QAGD,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAI,IAAI,EAAE,IAAI,CAAC,CAAC;QAC7C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,QAAqC,CAAC,CAAC;AAEhE,QAAA,OAAO,QAAuB,CAAC;KAChC,CAAA;AAED,IAAA,kBAAA,CAAA,SAAA,CAAA,YAAY,GAAZ,YAAA;QACE,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;KAC5C,CAAA;IACH,OAAC,kBAAA,CAAA;AAAD,CAAC,EAAA;;;;"}
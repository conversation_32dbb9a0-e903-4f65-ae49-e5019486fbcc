"use strict";
/**
 * Copyright 2024 Google LLC. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorQueryOptions = void 0;
class VectorQueryOptions {
    constructor(limit, distanceMeasure) {
        this.limit = limit;
        this.distanceMeasure = distanceMeasure;
    }
    isEqual(other) {
        if (this === other) {
            return true;
        }
        if (!(other instanceof VectorQueryOptions)) {
            return false;
        }
        return (this.limit === other.limit &&
            this.distanceMeasure === other.distanceMeasure);
    }
}
exports.VectorQueryOptions = VectorQueryOptions;
//# sourceMappingURL=vector-query-options.js.map